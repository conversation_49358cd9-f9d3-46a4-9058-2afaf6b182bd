{"main": {"id": "4b878e38909cb33c", "type": "split", "children": [{"id": "2a5028ed071b8a65", "type": "tabs", "children": [{"id": "a82ddab9044be2af", "type": "leaf", "state": {"type": "markdown", "state": {"file": "تقرير_النظام_للعميل.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "تقرير_النظام_للعميل"}}]}], "direction": "vertical"}, "left": {"id": "d8516d16aa26704a", "type": "split", "children": [{"id": "ecc3e6280d008f81", "type": "tabs", "children": [{"id": "2b59b8c8e7530697", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "97fa223aaf4ecf0d", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "bfeb683ba87819f1", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 348.5}, "right": {"id": "140eda10b8f4c95c", "type": "split", "children": [{"id": "9db3d4078412303e", "type": "tabs", "children": [{"id": "938adb560fcac570", "type": "leaf", "state": {"type": "backlink", "state": {"file": "README.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for README"}}, {"id": "992bebc7893538a3", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "README.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from README"}}, {"id": "7a42be3c10c2dbd1", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "cf01068370b42b7a", "type": "leaf", "state": {"type": "outline", "state": {"file": "README.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of README"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "a82ddab9044be2af", "lastOpenFiles": ["خطة_تطوير_الواجهة_الأمامية_الشاملة.md", "تقرير شامل لتطوير متجر إلكتروني (نسخة مطورة من سلة).md", "تقرير_النظام_للعميل.md", "README.md", "04 - Features Breakdown/07 - Store Settings.md", "04 - Features Breakdown/06 - Reviews & Ratings.md", "04 - Features Breakdown/05 - Content Management.md", "04 - Features Breakdown/04 - Marketing & Promotions.md", "04 - Features Breakdown/03 - Customer Management.md", "04 - Features Breakdown/02 - Order Management.md", "04 - Features Breakdown/01 - Product Management.md", "03 - Frontend Development (Next.js)/Phase 5 - Optimization & Launch.md", "03 - Frontend Development (Next.js)/Phase 4 - Advanced UX & Admin Panel.md", "03 - Frontend Development (Next.js)/Phase 3 - Checkout & User Account.md", "03 - Frontend Development (Next.js)/Phase 2 - E-commerce Core.md", "03 - Frontend Development (Next.js)/Phase 1 - Core Setup.md", "02 - Backend Development (Django DRF)/Phase 5 - Deployment & Optimization.md", "02 - Backend Development (Django DRF)/Phase 4 - Advanced Features.md", "02 - Backend Development (Django DRF)/Phase 3 - Checkout & Integrations.md", "02 - Backend Development (Django DRF)/Phase 2 - E-commerce Core.md", "02 - Backend Development (Django DRF)/Phase 1 - Core Setup.md", "01 - System Design/Workflows/03 - User Registration & Verification.md", "01 - System Design/Workflows/02 - Inventory Management.md", "01 - System Design/Workflows/01 - Order Lifecycle.md", "01 - System Design/Frontend Pages (Next.js)/04 - Admin Panel Pages.md", "01 - System Design/Frontend Pages (Next.js)/03 - User Account Pages.md", "01 - System Design/Workflows"]}