# مخطط سير العمل: تسجيل مستخدم جديد والتحقق

هذا المخطط يوضح عملية تسجيل مستخدم جديد وتفعيل حسابه.

```mermaid
sequenceDiagram
    participant User as المستخدم
    participant Frontend as الواجهة الأمامية (Next.js)
    participant Backend as الواجهة الخلفية (Django)
    participant EmailService as خدمة البريد الإلكتروني

    User->>Frontend: يملأ نموذج التسجيل ويضغط "إنشاء حساب"
    Frontend->>Backend: POST /api/auth/register/ (مع بيانات المستخدم)

    Backend->>Backend: التحقق من صحة البيانات (Validation)
    alt البيانات غير صالحة
        Backend-->>Frontend: إرجاع خطأ (e.g., 400 Bad Request) مع تفاصيل الأخطاء
        Frontend-->>User: عرض رسائل الخطأ بجانب الحقول
    end

    alt البيانات صالحة
        Backend->>Backend: إنشاء سجل `CustomUser` مع `is_active = False`
        Backend->>Backend: إنشاء رمز تفعيل فريد (Activation Token)
        Backend->>EmailService: طلب إرسال بريد تفعيل (مع الرمز)
        EmailService-->>User: إرسال بريد إلكتروني يحتوي على رابط التفعيل
        Backend-->>Frontend: إرجاع رسالة نجاح (e.g., 201 Created)
        Frontend-->>User: عرض رسالة "تم التسجيل، يرجى مراجعة بريدك الإلكتروني للتفعيل"
    end

    User->>User: يفتح البريد ويضغط على رابط التفعيل
    User->>Frontend: زيارة رابط التفعيل (e.g., /activate?token=...)
    Frontend->>Backend: POST /api/auth/activate/ (مع رمز التفعيل)

    Backend->>Backend: التحقق من صحة الرمز
    alt الرمز صالح
        Backend->>Backend: تفعيل الحساب (`is_active = True`)
        Backend-->>Frontend: إرجاع رسالة نجاح
        Frontend-->>User: عرض رسالة "تم تفعيل حسابك بنجاح" وتوجيهه لصفحة تسجيل الدخول
    else الرمز غير صالح أو منتهي الصلاحية
        Backend-->>Frontend: إرجاع خطأ
        Frontend-->>User: عرض رسالة خطأ مناسبة
    end
```

### أفضل الممارسات المطبقة:
-   **عدم تفعيل الحساب مباشرة:** تعيين `is_active = False` عند التسجيل يجبر المستخدم على تأكيد ملكيته للبريد الإلكتروني، مما يقلل من الحسابات الوهمية والبريد المزعج.
-   **رموز تفعيل قصيرة الأجل:** يجب أن يكون لرمز التفعيل تاريخ انتهاء صلاحية (e.g., 24 ساعة) لزيادة الأمان.
-   **رسائل واضحة للمستخدم:** إرشاد المستخدم بوضوح حول الخطوات التالية في كل مرحلة من مراحل العملية.
