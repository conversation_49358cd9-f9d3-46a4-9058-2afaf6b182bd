# مخطط سير العمل: إدارة المخزون (Inventory Management)

هذا المخطط يوضح كيفية تأثر المخزون بالعمليات المختلفة في النظام.

```mermaid
graph TD
    subgraph A [العميل]
        A1(إضافة للسلة)
        A2(إتمام الطلب)
    end

    subgraph B [النظام]
        B1{التحقق من توفر المخزون}
        B2(حجز مؤقت للمخزون - اختياري)
        B3(خصم المخزون من `ProductVariant.stock`)
        B4(إعادة المخزون إلى `ProductVariant.stock`)
    end

    subgraph C [المسؤول]
        C1(إلغاء الطلب)
        C2(الموافقة على الإرجاع)
        C3(تعديل المخزون يدوياً)
    end

    A1 -- يطلب 1 وحدة --> B1
    B1 -- متوفر --> A2
    B1 -- غير متوفر --> A1_Fail(إظها<PERSON> رسالة "غير متوفر")

    A2 -- إنشاء طلب ناجح --> B3

    C1 -- إلغاء --> B4
    C2 -- إرجاع مكتمل --> B4
    C3 -- تعديل --> B3
    C3 -- تعديل --> B4

```

### تفصيل العملية:

1.  **التحقق عند الإضافة للسلة:** عند محاولة العميل إضافة منتج إلى السلة، يتحقق النظام من أن الكمية المطلوبة أقل من أو تساوي الكمية المتاحة في المخزون (`ProductVariant.stock`).
2.  **الحجز المؤقت (اختياري):** يمكن تطبيق نظام حجز مؤقت للمخزون لمدة قصيرة (e.g., 15 دقيقة) بعد إضافة المنتج للسلة لضمان توفره حتى إتمام الدفع.
3.  **خصم المخزون:** عند تأكيد الطلب بنجاح (بعد الدفع)، يتم خصم كمية المنتجات المطلوبة بشكل دائم من حقل `stock` في جدول `ProductVariant`. تتم هذه العملية داخل `atomic transaction` مع إنشاء الطلب.
4.  **إعادة المخزون:** يتم إعادة الكمية إلى المخزون في حالتين رئيسيتين:
    *   **إلغاء الطلب:** إذا تم إلغاء الطلب من قبل العميل أو المسؤول.
    *   **الإرجاع:** بعد استلام المنتج المرتجع والتأكد من حالته.
5.  **التعديل اليدوي:** يمكن للمسؤول تعديل كمية المخزون مباشرة من لوحة التحكم (لأسباب مثل جرد المخزون أو تلف البضاعة).
