# نقاط نهاية API: المصادقة (Authentication)

جميع هذه النقاط تقع تحت المسار `/api/auth/`.

## 1. تسجيل مستخدم جديد
- **Endpoint:** `POST /register/`
- **الوصف:** إنشاء حساب مستخدم جديد (عميل).
- **البيانات المطلوبة (Request Body):**
    ```json
    {
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "password": "complex_password123",
        "phone_number": "966501234567"
    }
    ```
- **الاستجابة (Success 201):**
    ```json
    {
        "message": "User registered successfully."
    }
    ```

## 2. تسجيل الدخول
- **Endpoint:** `POST /login/`
- **الوصف:** مصادقة المستخدم وإرجاع رموز JWT.
- **البيانات المطلوبة (Request Body):**
    ```json
    {
        "email": "<EMAIL>",
        "password": "complex_password123"
    }
    ```
- **الاستجابة (Success 200):**
    ```json
    {
        "access": "ey...",
        "refresh": "ey..."
    }
    ```

## 3. تسجيل الخروج
- **Endpoint:** `POST /logout/`
- **الوصف:** إضافة رمز التحديث (refresh token) إلى القائمة السوداء.
- **البيانات المطلوبة (Request Body):**
    ```json
    {
        "refresh": "ey..."
    }
    ```
- **الاستجابة (Success 204):** No Content.

## 4. طلب إعادة تعيين كلمة المرور
- **Endpoint:** `POST /password-reset/`
- **الوصف:** إرسال بريد إلكتروني يحتوي على رابط لإعادة تعيين كلمة المرور.
- **البيانات المطلوبة (Request Body):**
    ```json
    {
        "email": "<EMAIL>"
    }
    ```

## 5. تأكيد إعادة تعيين كلمة المرور
- **Endpoint:** `POST /password-reset/confirm/`
- **الوصف:** تعيين كلمة مرور جديدة باستخدام الرمز المرسل.
- **البيانات المطلوبة (Request Body):**
    ```json
    {
        "token": "a_unique_token",
        "password": "new_strong_password"
    }
    ```
