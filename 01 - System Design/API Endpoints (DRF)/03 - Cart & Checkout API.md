# نقاط نهاية API: السلة والدفع (Cart & Checkout)

## 1. عمليات سلة التسوق
- **Endpoint:** `/api/cart/`
- **الوصف:** مجموعة نقاط نهاية لإدارة سلة التسوق الخاصة بالمستخدم (تعتمد على الجلسة أو المستخدم المصادق عليه).

### عرض السلة
- **Method:** `GET`
- **Endpoint:** `/api/cart/`

### إضافة عنصر للسلة
- **Method:** `POST`
- **Endpoint:** `/api/cart/add/`
- **Body:**
    ```json
    {
        "variant_id": 123, // ID الخاص بمتغير المنتج
        "quantity": 1
    }
    ```

### تحديث كمية عنصر
- **Method:** `PUT`
- **Endpoint:** `/api/cart/update/<item_id>/`
- **Body:**
    ```json
    {
        "quantity": 2
    }
    ```

### حذف عنصر من السلة
- **Method:** `DELETE`
- **Endpoint:** `/api/cart/remove/<item_id>/`

### تطبيق كوبون
- **Method:** `POST`
- **Endpoint:** `/api/cart/apply-coupon/`
- **Body:**
    ```json
    {
        "code": "SALE50"
    }
    ```

## 2. عمليات الطلب (Checkout)
- **Endpoint:** `/api/orders/`
- **الوصف:** إنشاء طلب جديد من محتويات السلة الحالية. (تتطلب مصادقة).

### إنشاء طلب جديد
- **Method:** `POST`
- **Endpoint:** `/api/orders/`
- **Body:**
    ```json
    {
        "shipping_address_id": 1,
        "payment_method": "credit_card",
        "notes": "الرجاء ترك الشحنة عند الباب."
    }
    ```
- **الاستجابة (Success 201):**
    ```json
    {
        "order_number": "ORD-12345",
        "total_amount": "550.00",
        "payment_redirect_url": "https://payment.gateway/..." // إذا لزم الأمر
    }
    ```
