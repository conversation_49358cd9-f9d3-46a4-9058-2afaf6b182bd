# نقاط نهاية API: لوحة التحكم (Admin)

جميع هذه النقاط تتطلب مصادقة كمسؤول (`is_staff=True`) وتقع تحت المسار `/api/admin/`.

## 1. إدارة المنتجات
- **Endpoints:**
    - `GET /products/`
    - `POST /products/`
    - `GET /products/<id>/`
    - `PUT /products/<id>/`
    - `DELETE /products/<id>/`
- **الوصف:** عمليات CRUD كاملة على المنتجات والفئات والمتغيرات.

## 2. إدارة الطلبات
- **Endpoints:**
    - `GET /orders/`
    - `GET /orders/<id>/`
    - `PUT /orders/<id>/status/` (لتحديث حالة الطلب)
- **الوصف:** عرض الطلبات وتحديث حالتها وإضافة تعليقات.
- **مثال على تحديث الحالة:**
    - **Body for `PUT /orders/<id>/status/`:**
        ```json
        {
            "status": "shipped",
            "comment": "تم الشحن عبر Aramex برقم تتبع XXXXX"
        }
        ```

## 3. إدارة العملاء
- **Endpoints:**
    - `GET /customers/`
    - `GET /customers/<id>/`
    - `PUT /customers/<id>/` (لتحديث بيانات العميل أو إضافته لمجموعة)
- **الوصف:** عرض وتعديل بيانات العملاء.

## 4. إدارة التقييمات
- **Endpoints:**
    - `GET /reviews/`
    - `PUT /reviews/<id>/` (لتغيير حالة النشر `is_published`)
    - `DELETE /reviews/<id>/`
- **الوصف:** مراجعة تقييمات العملاء ونشرها أو حذفها.

## 5. إدارة الكوبونات
- **Endpoints:**
    - `GET /coupons/`
    - `POST /coupons/`
    - `PUT /coupons/<id>/`
    - `DELETE /coupons/<id>/`
- **الوصف:** عمليات CRUD كاملة على كوبونات الخصم.

## 6. إدارة الصفحات الثابتة
- **Endpoints:**
    - `GET /static-pages/`
    - `POST /static-pages/`
    - `PUT /static-pages/<id>/`
- **الوصف:** إدارة محتوى الصفحات التعريفية.

## 7. لوحة القيادة (Dashboard)
- **Endpoint:** `GET /dashboard/stats/`
- **الوصف:** جلب إحصائيات رئيسية لعرضها في لوحة التحكم (إجمالي المبيعات، عدد الطلبات الجديدة، إلخ).
