# نقاط نهاية API: ملف تعريف المستخدم (User Profile)

جميع هذه النقاط تتطلب مصادقة المستخدم وتقع تحت المسار `/api/profile/`.

## 1. الحصول على بيانات الملف الشخصي
- **Endpoint:** `GET /`
- **الوصف:** جلب بيانات المستخدم المسجل دخوله حالياً.
- **الاستجابة (Success 200):**
    ```json
    {
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "phone_number": "966501234567",
        "loyalty_points": 150
    }
    ```

## 2. تحديث بيانات الملف الشخصي
- **Endpoint:** `PUT /`
- **الوصف:** تحديث بيانات المستخدم.
- **البيانات المطلوبة (Request Body):**
    ```json
    {
        "first_name": "<PERSON>",
        "last_name": "<PERSON>",
        "phone_number": "966501112222"
    }
    ```

## 3. الحصول على سجل طلبات المستخدم
- **Endpoint:** `GET /orders/`
- **الوصف:** جلب قائمة بجميع الطلبات السابقة للمستخدم.
- **الاستجابة (Success 200):**
    ```json
    {
        "count": 5,
        "results": [
            {
                "order_number": "ORD-12345",
                "order_status": "delivered",
                "total_amount": "550.00",
                "created_at": "2025-07-15T10:00:00Z"
            }
        ]
    }
    ```

## 4. الحصول على تفاصيل طلب محدد
- **Endpoint:** `GET /orders/<order_number>/`
- **الوصف:** جلب التفاصيل الكاملة لطلب معين يخص المستخدم.

## 5. إدارة العناوين
- **Endpoint:** `GET /addresses/` (للحصول على قائمة العناوين)
- **Endpoint:** `POST /addresses/` (لإضافة عنوان جديد)
- **Endpoint:** `PUT /addresses/<address_id>/` (لتحديث عنوان)
- **Endpoint:** `DELETE /addresses/<address_id>/` (لحذف عنوان)
