# نقاط نهاية API: المنتجات (Products)

جميع هذه النقاط تقع تحت المسار `/api/products/`.

## 1. الحصول على قائمة المنتجات
- **Endpoint:** `GET /`
- **الوصف:** جلب قائمة بالمنتجات المنشورة مع دعم للفلترة، الترتيب، والترقيم (pagination).
- **مثال مع معاملات (Query Parameters):** `GET /?category=abayas&sort=-created_at&page=2`
- **الاستجابة (Success 200):**
    ```json
    {
        "count": 150,
        "next": "/api/products/?page=3",
        "previous": "/api/products/?page=1",
        "results": [
            {
                "id": 1,
                "name": "عباية كلوش مطرزة",
                "slug": "abaya-k-1",
                "price": "350.00",
                "main_image": "url/to/image.jpg",
                "average_rating": 4.5
            }
        ]
    }
    ```

## 2. الحصول على تفاصيل منتج
- **Endpoint:** `GET /<slug>/`
- **الوصف:** جلب التفاصيل الكاملة لمنتج معين باستخدام الـ slug.
- **الاستجابة (Success 200):**
    ```json
    {
        "id": 1,
        "name": "عباية كلوش مطرزة",
        "description": "وصف تفصيلي للمنتج...",
        "price": "350.00",
        "images": [...],
        "options": [...],
        "variants": [...],
        "reviews": [...]
    }
    ```

## 3. الحصول على قائمة الفئات
- **Endpoint:** `GET /categories/`
- **الوصف:** جلب شجرة الفئات (Categories Tree).

## 4. الحصول على منتجات فئة معينة
- **Endpoint:** `GET /categories/<slug>/`
- **الوصف:** جلب المنتجات التي تنتمي لفئة معينة.

## 5. إضافة تقييم لمنتج
- **Endpoint:** `POST /<product_id>/reviews/`
- **الوصف:** إضافة تقييم جديد لمنتج. (تتطلب مصادقة المستخدم).
- **البيانات المطلوبة (Request Body):**
    ```json
    {
        "rating": 5,
        "comment": "منتج رائع جداً!"
    }
    ```
- **الاستجابة (Success 201):** بيانات التقييم الذي تم إنشاؤه.
