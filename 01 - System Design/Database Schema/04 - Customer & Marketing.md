# مخطط قاعدة البيانات المفصل: العملاء والتسويق

## 1. نموذج التقييم (Review)
لتخزين تقييمات العملاء للمنتجات.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `product` | ForeignKey | `Product`, `on_delete=models.CASCADE`, `related_name='reviews'` | نعم |
| `customer` | ForeignKey | `CustomUser`, `on_delete=models.CASCADE`, `related_name='reviews'` | نعم |
| `order` | ForeignKey | `Order`, `on_delete=models.CASCADE` (للتحقق من أن العميل اشترى المنتج) | نعم |
| `rating` | PositiveSmallIntegerField | `validators=[MinValueValidator(1), MaxValueValidator(5)]` | نعم |
| `comment` | TextField | `blank=True` | لا |
| `status` | CharField | `choices=ReviewStatus.choices`, `default=ReviewStatus.PENDING` | نعم |
| `created_at` | DateTime | `auto_now_add=True` | نعم |
| **Constraint** | `unique_together = ('order', 'product', 'customer')` | | نعم |

## 2. نموذج الكوبون (Coupon)
لإدارة كوبونات الخصم.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `code` | CharField | `max_length=50`, `unique=True` | نعم |
| `discount_type`| CharField | `choices=DiscountType.choices` | نعم |
| `discount_value`| DecimalField | `max_digits=10`, `decimal_places=2` | لا |
| `min_order_amount`| DecimalField | `null=True`, `blank=True` | لا |
| `start_date` | DateTime | | نعم |
| `end_date` | DateTime | `null=True`, `blank=True` | نعم |
| `usage_limit` | PositiveIntegerField | `default=1` | لا |
| `is_active` | Boolean | `default=True` | نعم |
| `free_shipping`| Boolean | `default=False` | لا |
| `excluded_products`| ManyToManyField | `Product`, `blank=True` | لا |
| `excluded_categories`| ManyToManyField | `Category`, `blank=True` | لا |

## 3. نموذج الإعلان/البنر (Advertisement)
لإدارة البنرات الإعلانية.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `title` | CharField | `max_length=200` | لا |
| `image` | ImageField | `upload_to='banners/'` | لا |
| `link_url` | URLField | `blank=True` | لا |
| `location` | CharField | `max_length=100` (e.g., 'homepage_main') | نعم |
| `start_date` | DateTime | `null=True`, `blank=True` | نعم |
| `end_date` | DateTime | `null=True`, `blank=True` | نعم |
| `is_active` | Boolean | `default=True`, `db_index=True` | نعم |
