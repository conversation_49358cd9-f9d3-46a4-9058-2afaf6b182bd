# مخطط قاعدة البيانات المفصل: المحتوى والإعدادات

**أفضل الممارسات:** سيتم فصل إدارة المحتوى التسويقي (الصفحات والبنرات) إلى Headless CMS. ستبقى نماذج الإعدادات في Django.

## 1. نموذج طريقة الشحن (ShippingMethod)

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `name` | CharField | `max_length=100` | لا |
| `description` | JSONField | `blank=True` (لدعم تعدد اللغات) | لا |
| `price` | DecimalField | `max_digits=10`, `decimal_places=2` | لا |
| `is_active` | Boolean | `default=True`, `db_index=True` | نعم |

## 2. نموذج بوابة الدفع (PaymentGateway)

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `name` | CharField | `max_length=100` | لا |
| `code` | CharField | `max_length=50`, `unique=True` (e.g., 'stripe', 'cod') | نعم |
| `is_active` | Boolean | `default=True`, `db_index=True` | نعم |
| `credentials` | JSONField | `null=True`, `blank=True` (سيتم تشفيرها قبل الحفظ) | لا |

## 3. نموذج إعدادات المتجر (StoreSettings)
يستخدم `django-solo` لضمان وجود صف واحد فقط.

| الحقل (Field) | النوع (Type) | الخصائص والقيود |
| :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي |
| `store_name` | JSONField | `{"en": "My Store", "ar": "متجري"}` |
| `support_email`| EmailField | |
| `supported_currencies`| ArrayField | `models.CharField(max_length=3)`, `default=['SAR']` |
| `default_currency`| CharField | `max_length=3`, `default='SAR'` |
| `vat_percentage`| DecimalField | `default=15.0` |

**ملاحظة:** تم حذف `StaticPage` و `Advertisement` من هنا لأنه سيتم إدارتهما عبر Headless CMS حسب القرار المعماري ADR-003.
