# مخطط قاعدة البيانات المفصل: إدارة المستخدمين

## 1. نموذج المستخدم (CustomUser)
يرث من `AbstractUser` في Django. هذا هو النموذج الأساسي للمصادقة.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `email` | EmailField | `unique=True`, `db_index=True` | نعم |
| `phone_number`| CharField | `unique=True`, `null=True`, `blank=True` | نعم |
| `password` | Hashed | موروث من Django | تلقائي |
| `first_name` | CharField | `max_length=150` | لا |
| `last_name` | CharField | `max_length=150` | لا |
| `is_staff` | Boolean | `default=False` (للوصول للوحة التحكم) | نعم |
| `is_active` | Boolean | `default=True` | نعم |
| `date_joined` | DateTime | `auto_now_add=True` | نعم |

## 2. نموذج ملف تعريف العميل (CustomerProfile)
يوسع نموذج المستخدم بمعلومات خاصة بالعملاء.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `user` | OneToOneField | `CustomUser`, `on_delete=models.CASCADE`, `primary_key=True`, `related_name='profile'` | تلقائي |
| `loyalty_points`| PositiveIntegerField | `default=0` | نعم |
| `customer_group`| ForeignKey | `CustomerGroup`, `on_delete=models.SET_NULL`, `null=True`, `related_name='customers'` | نعم |

## 3. نموذج مجموعة العملاء (CustomerGroup)
لتصنيف العملاء.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `name` | CharField | `max_length=100`, `unique=True` | نعم |
| `description` | TextField | `blank=True` | لا |

## 4. نموذج العنوان (Address)
لتخزين عناوين شحن متعددة للعميل.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `customer` | ForeignKey | `CustomerProfile`, `on_delete=models.CASCADE`, `related_name='addresses'` | نعم |
| `full_name` | CharField | `max_length=255` | لا |
| `address_line_1`| CharField | `max_length=255` | لا |
| `city` | CharField | `max_length=100` | نعم |
| `country` | CharField | `max_length=100` | نعم |
| `zip_code` | CharField | `max_length=20` | نعم |
| `phone_number`| CharField | `max_length=20` | لا |
| `is_default` | Boolean | `default=False` | نعم |
