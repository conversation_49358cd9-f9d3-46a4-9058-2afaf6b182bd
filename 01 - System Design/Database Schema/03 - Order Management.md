# مخطط قاعدة البيانات المفصل: إدارة الطلبات

**أفضل الممارسات:** سيتم تغليف منطق إنشاء الطلب داخل معاملة ذرية (`atomic transaction`) لضمان سلامة البيانات (إما أن تنجح جميع العمليات أو تفشل جميعها).

## 1. نموذج الطلب (Order)
يحتوي على المعلومات التلخيصية للطلب.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `order_number`| UUIDField | `default=uuid.uuid4`, `editable=False`, `unique=True` | نعم |
| `customer` | ForeignKey | `CustomUser`, `on_delete=models.SET_NULL`, `null=True`, `related_name='orders'` | نعم |
| `status`| CharField | `max_length=50`, `choices=OrderStatus.choices`, `default=OrderStatus.PENDING` | نعم |
| `payment_status`| CharField | `max_length=50`, `choices=PaymentStatus.choices`, `default=PaymentStatus.PENDING` | نعم |
| `shipping_address`| JSONField | يخزن نسخة من عنوان الشحن وقت الطلب | لا |
| `billing_address`| JSONField | `null=True`, `blank=True` | لا |
| `subtotal` | DecimalField | `max_digits=10`, `decimal_places=2` | لا |
| `shipping_cost`| DecimalField | `max_digits=10`, `decimal_places=2` | لا |
| `discount_amount`| DecimalField | `max_digits=10`, `decimal_places=2`, `default=0` | لا |
| `total_amount`| DecimalField | `max_digits=10`, `decimal_places=2` | نعم |
| `notes` | TextField | `blank=True` | لا |
| `coupon` | ForeignKey | `Coupon`, `on_delete=models.SET_NULL`, `null=True`, `blank=True` | نعم |
| `created_at` | DateTime | `auto_now_add=True` | نعم |

## 2. نموذج عناصر الطلب (OrderItem)
يمثل كل منتج داخل طلب معين.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `order` | ForeignKey | `Order`, `on_delete=models.CASCADE`, `related_name='items'` | نعم |
| `variant` | ForeignKey | `ProductVariant`, `on_delete=models.PROTECT` (لمنع حذف متغير إذا كان في طلب) | نعم |
| `quantity` | PositiveIntegerField | | لا |
| `unit_price`| DecimalField | `max_digits=10`, `decimal_places=2` (سعر الوحدة وقت الشراء) | لا |
| `total_price`| DecimalField | `max_digits=10`, `decimal_places=2` | لا |

## 3. نموذج سجل الطلب (OrderLog)
لتسجيل تاريخ تغييرات حالة الطلب.

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `order` | ForeignKey | `Order`, `on_delete=models.CASCADE`, `related_name='logs'` | نعم |
| `timestamp` | DateTime | `auto_now_add=True` | نعم |
| `status` | CharField | `max_length=50` (الحالة الجديدة التي تم الانتقال إليها) | نعم |
| `comment` | TextField | `blank=True` (تعليق من المسؤول أو النظام) | لا |
| `user` | ForeignKey | `CustomUser`, `on_delete=models.SET_NULL`, `null=True` (المستخدم الذي أجرى التغيير) | نعم |
