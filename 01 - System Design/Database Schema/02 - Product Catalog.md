# مخطط قاعدة البيانات المفصل: كتالوج المنتجات

**أفضل الممارسات:** سيتم استخدام `django-mptt` لإدارة الفئات المتداخلة. سيتم تصميم النماذج لدعم تعدد اللغات (i18n) من البداية باستخدام حقول `JSONField`.

## 1. نموذج الفئة (Category)

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `name` | JSONField | `{"en": "T-shirts", "ar": "تي شيرتات"}` | نعم (GIN Index) |
| `slug` | SlugField | `unique=True`, `max_length=255` (سيكون باللغة الافتراضية) | نعم |
| `description` | JSONField | `null=True`, `blank=True` | لا |
| `parent` | TreeForeignKey | `self`, `on_delete=models.CASCADE`, `null=True`, `blank=True`, `related_name='children'` | تلقائي (mptt) |
| `is_active` | Boolean | `default=True`, `db_index=True` | نعم |

## 2. نموذج المنتج (Product)

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `name` | JSONField | `{"en": "Cool T-shirt", "ar": "تي شيرت رائع"}` | نعم (GIN Index) |
| `slug` | SlugField | `unique=True`, `max_length=300` | نعم |
| `description` | JSONField | `blank=True` | لا |
| `base_price` | DecimalField | `max_digits=10`, `decimal_places=2` | نعم |
| `category` | ForeignKey | `Category`, `on_delete=models.PROTECT`, `related_name='products'` | نعم |
| `is_active` | Boolean | `default=True`, `db_index=True` | نعم |
| `is_featured`| Boolean | `default=False`, `db_index=True` | نعم |
| `created_at` | DateTime | `auto_now_add=True` | نعم |
| `updated_at` | DateTime | `auto_now=True` | نعم |

## 3. نموذج متغير المنتج (ProductVariant)

| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `product` | ForeignKey | `Product`, `on_delete=models.CASCADE`, `related_name='variants'` | نعم |
| `price_override`| DecimalField | `null=True`, `blank=True` | نعم |
| `stock` | PositiveIntegerField | `default=0` | نعم |
| `sku` | CharField | `max_length=100`, `unique=True`, `blank=True`, `null=True` | نعم |

## 4. نماذج الخيارات (Options)

### 4.1. OptionGroup (مثل: "Size", "Color")
| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `name` | JSONField | `{"en": "Size", "ar": "المقاس"}` | نعم (GIN Index) |

### 4.2. OptionValue (مثل: "S", "Red")
| الحقل (Field) | النوع (Type) | الخصائص والقيود | الفهرسة (Indexing) |
| :--- | :--- | :--- | :--- |
| `id` | PK | مفتاح أساسي | تلقائي |
| `group` | ForeignKey | `OptionGroup`, `on_delete=models.CASCADE`, `related_name='values'` | نعم |
| `value` | JSONField | `{"en": "Red", "ar": "أحمر"}` | نعم (GIN Index) |

...
