# مخطط علاقات الكيانات (Entity-Relationship Diagram - ERD)

هذا المخطط يوضح الهيكل العام لقاعدة البيانات والعلاقات بين الجداول الرئيسية. تم تصميم هذا المخطط ليكون مرجعاً مرئياً سريعاً. للحصول على التفاصيل الدقيقة لكل حقل وعلاقة، يرجى الرجوع إلى مستندات مخطط قاعدة البيانات المفصلة.

-   [[01 - User Management]]
-   [[02 - Product Catalog]]
-   [[03 - Order Management]]
-   [[04 - Customer & Marketing]]

```mermaid
erDiagram
    CUSTOM_USER ||--o{ CUSTOMER_PROFILE : "has"
    CUSTOMER_PROFILE }o--|| CUSTOMER_GROUP : "belongs to"
    CUSTOMER_PROFILE ||--o{ ADDRESS : "has multiple"
    CUSTOM_USER ||--o{ "ORDER" : "places"
    CUSTOM_USER ||--o{ REVIEW : "writes"

    CATEGORY }o--|| CATEGORY : "can be sub-category of"
    CATEGORY ||--o{ PRODUCT : "contains"

    PRODUCT ||--o{ PRODUCT_IMAGE : "has multiple"
    PRODUCT ||--o{ PRODUCT_VARIANT : "has multiple"
    PRODUCT ||--o{ REVIEW : "is reviewed on"
    PRODUCT }o--o{ COUPON_EXCLUDED_PRODUCTS : "can be excluded by"

    PRODUCT_VARIANT }o--|| PRODUCT_OPTION_VALUE : "is defined by"
    PRODUCT_VARIANT ||--o{ ORDER_ITEM : "can be"

    "ORDER" ||--o{ ORDER_ITEM : "contains"
    "ORDER" }o--|| COUPON : "can have"
    "ORDER" ||--o{ ORDER_LOG : "has"
    "ORDER" }o--|| SHIPPING_METHOD : "uses"

    COUPON ||--o{ COUPON_EXCLUDED_PRODUCTS : "excludes"
    COUPON }o--o{ COUPON_EXCLUDED_CATEGORIES : "excludes"
    CATEGORY }o--o{ COUPON_EXCLUDED_CATEGORIES : "can be excluded by"

    OPTION_GROUP ||--o{ OPTION_VALUE : "has multiple"
    PRODUCT_VARIANT }o--o{ OPTION_VALUE : "is composed of"

```
**ملاحظة:** نماذج المحتوى مثل `StaticPage` و `Advertisement` تم نقلها إلى Headless CMS حسب القرار [[../00 - Project Plan/05 - Architectural Decisions#ADR-003|ADR-003]].
