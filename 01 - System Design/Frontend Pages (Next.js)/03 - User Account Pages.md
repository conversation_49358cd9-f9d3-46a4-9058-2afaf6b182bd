# صفحات الواجهة الأمامية: حساب المستخدم

هذه الصفحات خاصة بالمستخدمين المسجلين وتتطلب مصادقة للوصول إليها.

## 1. لوحة تحكم المستخدم (`/account/dashboard`)
- **الغرض:** صفحة رئيسية لحساب العميل.
- **المكونات:**
    - **Welcome Message:** رسالة ترحيب باسم العميل.
    - **Quick Stats:** نظرة سريعة على آخر طلب، نقاط الولاء، والإشعارات.
    - **Navigation Menu:** قائمة تنقل جانبية لبقية صفحات الحساب.

## 2. سجل الطلبات (`/account/orders`)
- **الغرض:** عرض قائمة بجميع الطلبات السابقة والحالية للعميل.
- **المكونات:**
    - **Orders List:** جدول أو قائمة بالطلبات.
        - لكل طلب: رقم الطلب، التاريخ، المبلغ الإجمالي، وحالة الطلب.
    - **View Details Button:** زر لعرض التفاصيل الكاملة للطلب.

## 3. تفاصيل الطلب (`/account/orders/[orderId]`)
- **الغرض:** عرض جميع تفاصيل طلب معين.
- **المكونات:**
    - **Order Information:** رقم الطلب، الحالة، التاريخ.
    - **Items Ordered:** قائمة بالمنتجات في الطلب.
    - **Shipping Information:** عنوان الشحن وتفاصيل التتبع.
    - **Payment Information:** طريقة الدفع والمبلغ المدفوع.
    - **Action Buttons:** زر "تكرار الطلب" أو "طلب إرجاع".

## 4. إدارة العناوين (`/account/addresses`)
- **الغرض:** السماح للعميل بإضافة، تعديل، وحذف عناوين الشحن.
- **المكونات:**
    - **Saved Addresses List:** قائمة بالعناوين المحفوظة.
    - **Add New Address Form:** نموذج لإضافة عنوان جديد.
    - **Set as Default:** خيار لجعل عنوان معين هو الافتراضي.

## 5. تفاصيل الحساب (`/account/profile`)
- **الغرض:** تعديل المعلومات الشخصية وتغيير كلمة المرور.
- **المكونات:**
    - **Profile Form:** نموذج لتحديث الاسم الأول، الأخير، ورقم الهاتف.
    - **Change Password Form:** نموذج لتغيير كلمة المرور.

## 6. نقاط الولاء (`/account/points`)
- **الغرض:** عرض سجل نقاط الولاء المكتسبة والمستخدمة.
- **المكونات:**
    - **Current Balance:** الرصيد الحالي للنقاط.
    - **Points History:** جدول يوضح تاريخ اكتساب واستخدام النقاط.
