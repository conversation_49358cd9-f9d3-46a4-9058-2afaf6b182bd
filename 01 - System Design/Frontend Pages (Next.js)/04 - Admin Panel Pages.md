# صفحات الواجهة الأمامية: لوحة تحكم المسؤول (Admin Panel)

هذه الواجهة ستكون تطبيق Next.js منفصل أو جزء محمي من التطبيق الرئيسي، مخصص لإدارة المتجر.

## 1. لوحة القيادة (`/admin/dashboard`)
- **الغرض:** عرض نظرة عامة وشاملة على أداء المتجر.
- **المكونات:**
    - **Key Metrics:** بطاقات عرض للمقاييس الهامة (إجمالي المبيعات، عدد الطلبات، عدد العملاء الجدد، متوسط قيمة الطلب).
    - **Sales Chart:** رسم بياني يوضح المبيعات خلال فترة زمنية (يوم، أسبوع، شهر).
    - **Recent Activities:** قائمة بآخر الطلبات، التقييمات، والعملاء المسجلين.
    - **Pending Actions:** إشعارات للإجراءات التي تتطلب تدخلاً (مثل طلبات بانتظار المراجعة).

## 2. إدارة المنتجات (`/admin/products`)
- **الغرض:** التحكم الكامل في كتالوج المنتجات.
- **المكونات:**
    - **Products Table:** جدول يعرض جميع المنتجات مع خيارات للبحث والفلترة.
    - **Add/Edit Product Page:** صفحة تحتوي على نموذج شامل لإضافة أو تعديل منتج (التفاصيل، الأسعار، الصور، المتغيرات، SEO).

## 3. إدارة الطلبات (`/admin/orders`)
- **الغرض:** متابعة وإدارة جميع طلبات العملاء.
- **المكونات:**
    - **Orders Table:** جدول يعرض جميع الطلبات مع فلاتر متقدمة (حسب الحالة، التاريخ، العميل).
    - **Order Details Page:** صفحة لعرض تفاصيل طلب معين، تحديث حالته، إضافة تعليقات، وطباعة الفاتورة.

## 4. إدارة العملاء (`/admin/customers`)
- **الغرض:** عرض وإدارة قاعدة بيانات العملاء.
- **المكونات:**
    - **Customers Table:** قائمة بالعملاء مع إمكانية البحث والتصنيف.
    - **Customer Details Page:** صفحة تعرض ملف العميل الكامل (معلوماته، سجل طلباته، نقاطه).

## 5. إدارة التسويق (`/admin/marketing`)
- **الغرض:** التحكم في الأدوات التسويقية.
- **المكونات:**
    - **Coupons Management:** واجهة لإنشاء وتعديل كوبونات الخصم.
    - **Banners Management:** واجهة لإدارة البنرات الإعلانية في المتجر.

## 6. إدارة المحتوى (`/admin/content`)
- **الغرض:** التحكم في الصفحات الثابتة.
- **المكونات:**
    - **Static Pages List:** قائمة بالصفحات التعريفية.
    - **Page Editor:** محرر نصوص متقدم (WYSIWYG) لتعديل محتوى الصفحات.

## 7. الإعدادات (`/admin/settings`)
- **الغرض:** التحكم في الإعدادات العامة للمتجر.
- **المكونات:**
    - **Store Settings:** إعدادات (العملة، الضرائب، معلومات الاتصال).
    - **Shipping Settings:** إدارة طرق الشحن.
    - **Payment Settings:** إدارة بوابات الدفع.
    - **Review Settings:** التحكم في إعدادات نشر التقييمات.
