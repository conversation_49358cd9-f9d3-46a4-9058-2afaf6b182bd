# صفحات الواجهة الأمامية: الصفحات العامة

هذه الصفحات متاحة لجميع الزوار.

## 1. الصفحة الرئيسية (`/`)
- **الغرض:** نقطة الدخول الرئيسية للمتجر.
- **المكونات:**
    - **Hero Banner:** بنر إعلاني رئيسي متحرك (Carousel) يعرض أحدث العروض. (بيانات من `Advertisement` API).
    - **Featured Categories:** عرض لأهم فئات المنتجات مع صور جذابة. (بيانات من `Category` API).
    - **Featured Products:** قائمة بالمنتجات المميزة أو الأكثر مبيعاً. (بيانات من `Product` API).
    - **New Arrivals:** قسم يعرض أحدث المنتجات المضافة.
    - **Newsletter Subscription:** نموذج للاشتراك في النشرة البريدية.
    - **Footer:** يحتوي على روابط مهمة، معلومات الاتصال، ووسائل التواصل الاجتماعي.
- **نوع العرض:** SSG (Static Site Generation) مع ISR (Incremental Static Regeneration) لتحديث البيانات بشكل دوري.

## 2. صفحات الفئات (`/category/[slug]`)
- **الغرض:** عرض جميع المنتجات ضمن فئة معينة.
- **المكونات:**
    - **Category Header:** اسم ووصف الفئة.
    - **Product Grid/List:** شبكة عرض للمنتجات.
    - **Filtering Sidebar:** فلاتر لتصفية المنتجات (حسب السعر، المقاس، اللون، التقييم).
    - **Sorting Options:** خيارات لترتيب المنتجات (الأحدث، الأعلى سعراً، الأقل سعراً).
    - **Pagination:** لتقسيم المنتجات على عدة صفحات.
- **نوع العرض:** SSR (Server-Side Rendering) لضمان عرض أحدث المنتجات والأسعار.

## 3. الصفحات الثابتة (`/pages/[slug]`)
- **الغرض:** عرض محتوى الصفحات التعريفية.
- **أمثلة:**
    - `/pages/about-us` (من نحن)
    - `/pages/privacy-policy` (سياسة الخصوصية)
    - `/pages/return-policy` (سياسة الاستبدال والاسترجاع)
    - `/pages/size-guide` (دليل القياس)
- **المكونات:**
    - **Page Title:** عنوان الصفحة.
    - **Content Body:** المحتوى المعروض الذي يتم جلبه من الـ API (يدعم HTML).
- **نوع العرض:** SSG (Static Site Generation) لأن محتواها نادراً ما يتغير.
