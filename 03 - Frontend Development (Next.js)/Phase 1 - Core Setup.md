# الواجهة الأمامية - المرحلة الأولى: نظام التصميم والأساسيات (أسابيع 1-4)

**التركيز على:** بناء نظام تصميم قوي باستخدام Storybook، وتأسيس بنية أمامية قابلة للتطوير.

## المهام

### الأسبوع 1: إعداد المشروع و Storybook
- [ ] **(Setup)** تهيئة مشروع Next.js باستخدام `TypeScript`, `ESLint`, `Prettier`.
- [ ] **(DX)** إعداد ودمج **Storybook** في المشروع.
- [ ] **(Styling)** إعداد `Tailwind CSS` مع ملف تكوين للموضوع (Theme).
- [ ] **(CI/CD)** إعداد خط أنابيب CI (Lint, Type-check, Build) ونشر Storybook تلقائياً (e.g., on Chromatic).

### الأسبوع 2: بناء نظام التصميم (Design System)
- [ ] **(Component)** بناء المكونات الذرية (Atoms) الأساسية في Storybook: `Button`, `Input`, `Card`, `Typography`.
- [ ] **(Component)** بناء الجزيئات (Molecules) في Storybook: `SearchBar`, `ProductCard`, `FormField`.
- [ ] **(Architecture)** هيكلة المجلدات بناءً على نهج التصميم الذري.

### الأسبوع 3: التخطيط العام والمصادقة
- [ ] **(Component)** بناء مكونات التخطيط (Organisms): `Header`, `Footer`, `Layout` باستخدام المكونات المبنية.
- [ ] **(Pages)** إنشاء صفحات المصادقة (`/login`, `/register`) باستخدام `React Hook Form`.
- [ ] **(API)** إنشاء خدمة API مركزية (e.g., `axios`) وربط نماذج المصادقة.
- [ ] **(State Management)** إعداد `React Context` لإدارة حالة المصادقة.

### الأسبوع 4: عرض المنتجات والمحتوى
- [ ] **(Data Fetching)** استخدام `SWR` أو `React Query` لجلب البيانات.
- [ ] **(Pages)** إنشاء صفحة رئيسية (`/`) تعرض المنتجات المميزة من API Django.
- [ ] **(CMS)** ربط الواجهة الأمامية مع Headless CMS لجلب وعرض البنرات الإعلانية.
- [ ] **(Pages)** إنشاء صفحة فئة ديناميكية (`/category/[slug]`).
- [ ] **(i18n)** إعداد `next-i18next` لدعم تعدد اللغات في الواجهة.

