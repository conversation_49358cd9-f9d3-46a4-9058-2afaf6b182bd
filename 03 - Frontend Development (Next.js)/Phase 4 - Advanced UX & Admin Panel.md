# الواجهة الأمامية - المرحلة الرابعة: تجربة المستخدم المتقدمة ولوحة التحكم (أسابيع 13-16)

## الأهداف
- تطبيق الميزات التسويقية (الكوبونات، نقاط الولاء).
- بناء واجهة البحث والفلاتر.
- البدء في بناء لوحة تحكم المسؤول.

## المهام

### الأسبوع 13: الكوبونات والبحث
- [ ] تفعيل حقل الكوبون في صفحة السلة وربطه بنقطة نهاية `POST /api/cart/apply-coupon/`.
- [ ] عرض الخصم المطبق في ملخص الطلب.
- [ ] إضافة شريط بحث في الـ Header.
- [ ] إنشاء صفحة نتائج البحث (`/search?q=...`) تعرض المنتجات المطابقة.

### الأسبوع 14: الفلاتر المتقدمة ونقاط الولاء
- [ ] بناء واجهة الفلاتر في صفحة الفئة (Sidebar) للتصفية حسب السعر، المتغيرات، والتقييم.
- [ ] ربط الفلاتر مع معاملات الـ API (`/api/products/?...`).
- [ ] إنشاء صفحة (`/account/points`) لعرض رصيد وسجل نقاط الولاء للمستخدم.

### الأسبوع 15: بدء لوحة تحكم المسؤول (Admin Panel)
- [ ] إعداد هيكل لوحة التحكم كجزء محمي من التطبيق (`/admin/*`).
- [ ] بناء التخطيط العام للوحة التحكم (قائمة تنقل جانبية، شريط علوي).
- [ ] تطوير لوحة القيادة الرئيسية (`/admin/dashboard`) وعرض الإحصائيات من الـ API.
- [ ] بناء صفحة إدارة المنتجات (`/admin/products`) مع جدول يعرض المنتجات.

### الأسبوع 16: استكمال لوحة التحكم (CRUD)
- [ ] بناء النماذج (Forms) اللازمة لإنشاء وتعديل المنتجات (`/admin/products/new`, `/admin/products/edit/[id]`).
- [ ] بناء صفحة إدارة الطلبات (`/admin/orders`) مع جدول للطلبات.
- [ ] بناء صفحة تفاصيل الطلب للمسؤول، مع إمكانية تحديث حالة الطلب.
