# الواجهة الأمامية - المرحلة الثالثة: الدفع وحساب المستخدم (أسابيع 9-12)

## الأهداف
- بناء واجهة مسار الدفع (Checkout).
- تطوير صفحات حساب المستخدم.
- عرض التقييمات والصفحات الثابتة.

## المهام

### الأسبوع 9: مسار الدفع (Checkout)
- [ ] إنشاء صفحة الدفع (`/checkout`) كمسار محمي.
- [ ] تصميم نموذج متعدد الخطوات أو في صفحة واحدة لجمع:
    -   معلومات الشحن (مع إمكانية استخدام العناوين المحفوظة).
    -   اختيار طريقة الشحن.
    -   اختيار طريقة الدفع.
- [ ] ربط زر "إتمام الطلب" بنقطة نهاية `POST /api/orders/`.

### الأسبوع 10: التكامل مع الدفع الإلكتروني
- [ ] تكامل واجهة الدفع مع مكتبة React الخاصة ببوابة الدفع (e.g., `@stripe/react-stripe-js`).
- [ ] التعامل مع إعادة توجيه المستخدم إلى صفحة الدفع الخارجية أو عرض نموذج البطاقة مباشرة.
- [ ] إنشاء صفحة تأكيد الطلب (`/order/success/[orderId]`) لعرضها بعد إتمام الدفع بنجاح.
- [ ] إنشاء صفحة لفشل عملية الدفع.

### الأسبوع 11: صفحات حساب المستخدم
- [ ] بناء صفحات قسم حسابي (`/account/*`):
    - [ ] `/account/dashboard` (لوحة التحكم الرئيسية).
    - [ ] `/account/orders` (قائمة الطلبات).
    - [ ] `/account/orders/[orderId]` (تفاصيل طلب معين).
    - [ ] `/account/addresses` (إدارة العناوين).
- [ ] ربط جميع هذه الصفحات بنقاط نهاية API الخاصة بملف المستخدم.

### الأسبوع 12: التقييمات والمحتوى الثابت
- [ ] إضافة قسم عرض التقييمات في صفحة تفاصيل المنتج.
- [ ] إنشاء نموذج لإضافة تقييم جديد (يظهر فقط للعملاء الذين اشتروا المنتج).
- [ ] إنشاء صفحة ديناميكية (`/pages/[slug]`) لعرض محتوى الصفحات الثابتة (مثل سياسة الخصوصية) التي يتم جلبها من الـ API.
- [ ] استخدام `getStaticProps` و `getStaticPaths` لهذه الصفحات لتحسين الأداء.
