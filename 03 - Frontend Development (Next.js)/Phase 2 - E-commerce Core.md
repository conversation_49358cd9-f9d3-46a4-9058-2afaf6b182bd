# الواجهة الأمامية - المرحلة الثانية: وظائف التجارة الإلكترونية الأساسية (أسابيع 5-8)

## الأهداف
- تطوير صفحة تفاصيل المنتج مع دعم المتغيرات.
- بناء واجهة سلة التسوق.
- ربط وظائف السلة مع الـ API.

## المهام

### الأسبوع 5: صفحة تفاصيل المنتج
- [ ] إنشاء صفحة ديناميكية لتفاصيل المنتج (`/product/[slug]`).
- [ ] بناء معرض صور المنتج (Product Image Gallery/Carousel).
- [ ] عرض جميع معلومات المنتج (الاسم، السعر، الوصف).
- [ ] استخدام `getServerSideProps` لجلب بيانات المنتج لضمان حداثة البيانات و SEO.

### الأسبوع 6: دعم متغيرات المنتج
- [ ] تطوير واجهة المستخدم لاختيار خيارات المنتج (أزرار للمقاس، دوائر ألوان للون).
- [ ] إضافة منطق لتحديث السعر، الصورة المعروضة، وحالة التوفر بناءً على المتغير الذي يختاره المستخدم.
- [ ] تعطيل زر "إضافة للسلة" إذا كان المتغير المختار غير متوفر في المخزون.

### الأسبوع 7: بناء واجهة سلة التسوق
- [ ] إنشاء صفحة سلة التسوق (`/cart`).
- [ ] بناء مكونات لعرض عناصر السلة (`CartItem`) وملخص الطلب (`OrderSummary`).
- [ ] تطوير واجهة المستخدم لتحديث كمية المنتجات أو حذفها من السلة.
- [ ] إضافة حقل لإدخال كوبون الخصم (سيتم تفعيله لاحقاً).

### الأسبوع 8: ربط سلة التسوق بالـ API
- [ ] ربط زر "إضافة للسلة" في صفحة المنتج بنقطة نهاية `POST /api/cart/add/`.
- [ ] ربط صفحة السلة بنقاط نهاية الـ API:
    - [ ] `GET /api/cart/` لجلب بيانات السلة عند تحميل الصفحة.
    - [ ] `PUT /api/cart/update/...` عند تغيير الكمية.
    - [ ] `DELETE /api/cart/remove/...` عند حذف عنصر.
- [ ] استخدام React Context أو Redux لإدارة حالة السلة بشكل عام في التطبيق (مثل عرض عدد المنتجات في أيقونة السلة بالـ Header).
