# 2. التقنيات المستخدمة (Technology Stack)

لتحقيق أهداف المشروع، تم اختيار مجموعة من التقنيات الحديثة والموثوقة التي تضمن الأداء العالي وقابلية التوسع.

## الواجهة الخلفية (Backend)

-   **إطار العمل (Framework):** Django & Django REST Framework (DRF)
-   **قاعدة البيانات (Database):** PostgreSQL
-   **مهام الخلفية غير المتزامنة (Async Tasks):** Celery
-   **وسيط الرسائل والتخزين المؤقت (Message Broker & Caching):** Redis
-   **البحث (Search):** **Elasticsearch / Meilisearch** (كمكون أساسي)
-   **لغة البرمجة:** Python

## الواجهة الأمامية (Frontend)

-   **إطار العمل (Framework):** Next.js
-   **مكتبة الواجهة (UI Library):** React
-   **التصميم (Styling):** Tailwind CSS
-   **إدارة الحالة (State Management):** React Context API / Redux Toolkit
-   **جلب البيانات من الـ API:** **SWR / React Query**
-   **تطوير المكونات المعزولة:** **Storybook**
-   **لغة البرمجة:** TypeScript

## إدارة المحتوى (Content Management)

-   **النظام:** **Headless CMS (e.g., Strapi, Sanity, Contentful)**

## النشر والبنية التحتية (Deployment & Infrastructure)

-   **الحاويات (Containerization):** Docker
-   **إدارة البنية التحتية ككود (IaC):** **Terraform / Ansible**
-   **استضافة الواجهة الخلفية:** AWS EC2 / DigitalOcean Droplets
-   **استضافة الواجهة الأمامية:** Vercel
-   **تخزين الوسائط (Media Storage):** AWS S3 / DigitalOcean Spaces
-   **التكامل والنشر المستمر (CI/CD):** GitHub Actions
-   **المراقبة وتتبع الأخطاء (Monitoring & Error Tracking):** Sentry, Prometheus, Grafana
-   **تحليلات البيانات (Data Analytics):** **AWS Kinesis / Kafka -> Google BigQuery -> Metabase/Tableau**
