# 5. القرارات المعمارية الرئيسية (Architectural Decision Records - ADRs)

هذا المستند يسجل القرارات الهندسية الهامة التي تم اتخاذها وتبريراتها.

## ADR-001: اختيار بنية "المونوليث المعياري" (Modular Monolith)

-   **القرار:** البدء ببنية "مونوليث معياري" لتطبيق Django، مع الفصل الصارم بين الميزات (التطبيقات) المختلفة مثل `users`, `products`, `orders`.
-   **التبرير:** هذه البنية تسرع من عملية التطوير الأولية وتقلل من التعقيد التشغيلي (مقارنة بالخدمات المصغرة). الفصل الصارم بين التطبيقات يسهل عملية استخراج أي تطبيق كخدمة مصغرة (Microservice) في المستقبل عند الحاجة (مثل فصل خدمة `notifications` أو `payments`).
-   **الآثار:** يجب الحفاظ على عدم وجود اعتماديات دائرية (circular dependencies) بين التطبيقات.

## ADR-002: اعتماد "نظام تصميم" و Storybook للواجهة الأمامية

-   **القرار:** تطوير "نظام تصميم" (Design System) داخلي، واستخدام **Storybook** لبناء واختبار مكونات React في بيئة معزولة.
-   **التبرير:** يضمن اتساق واجهة المستخدم، يسرع من عملية تطوير الميزات الجديدة، ويسهل التعاون بين المطورين والمصممين. Storybook يسمح بتطوير واختبار المكونات بشكل مستقل عن منطق العمل.

## ADR-003: فصل إدارة المحتوى باستخدام Headless CMS

-   **القرار:** استخدام **Headless CMS** (مثل Strapi, Sanity, أو Contentful) لإدارة المحتوى التسويقي (الصفحات الثابتة، البنرات، مقالات المدونة) بدلاً من النماذج المدمجة في Django.
-   **التبرير:** هذا القرار يفصل تماماً بين المحتوى والكود. يمكّن فريق التسويق من إنشاء وتعديل المحتوى بسرعة وأمان دون الحاجة لنشر إصدار جديد من التطبيق. كما أنه يبسط الواجهة الخلفية لـ Django للتركيز فقط على منطق التجارة الإلكترونية.

## ADR-004: بنية تحتية للتحليلات قائمة على الأحداث (Event-Driven Analytics)

-   **القرار:** تصميم نظام تحليلات يعتمد على إرسال أحداث (Events) من الواجهة الخلفية والأمامية إلى ناقل رسائل (Message Bus) مثل **AWS Kinesis** أو **Kafka**.
-   **التبرير:** بدلاً من الاعتماد على تحليلات سطحية، يتيح لنا هذا النهج جمع بيانات غنية ومفصلة عن سلوك المستخدمين. يمكن بعد ذلك توجيه هذه الأحداث إلى وجهات متعددة: مستودع بيانات (Data Warehouse) مثل **Google BigQuery** لتحليل ذكاء الأعمال (BI)، أو خدمات أخرى لمراقبة الأداء في الوقت الفعلي.

## ADR-005: تصميم يدعم العالمية (Internationalization - i18n)

-   **القرار:** تصميم نماذج قاعدة البيانات لدعم تعدد اللغات من البداية باستخدام حقول `JSONField` لتخزين الترجمات.
-   **التبرير:** إضافة دعم تعدد اللغات لاحقاً عملية معقدة ومكلفة. تصميم المخطط لدعمها من البداية (حتى لو تم إطلاق لغة واحدة فقط) يجعل التوسع العالمي في المستقبل أسهل بكثير.
