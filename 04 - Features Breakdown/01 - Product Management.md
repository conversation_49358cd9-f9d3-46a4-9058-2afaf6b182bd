# تفصيل الميزات: إدارة المنتجات

## 1. نظرة عامة
تتيح هذه الميزة لمدير المتجر التحكم الكامل في كتالوج المنتجات، بما في ذلك إضافة منتجات جديدة، تعديل الحالية، تنظيمها في فئات، وإدارة المخزون.

## 2. التصميم الفني المرتبط
-   **مخطط قاعدة البيانات:** [[../01 - System Design/Database Schema/02 - Product Catalog]]
-   **نقاط نهاية الـ API:** [[../01 - System Design/API Endpoints (DRF)/02 - Products API]]
-   **سير عمل المخزون:** [[../01 - System Design/Workflows/02 - Inventory Management]]

## 3. المتطلبات الوظيفية
...

### لوحة التحكم (Admin)
- **إضافة منتج جديد:**
    - تحديد نوع المنتج (جاهز، رقمي، خدمة).
    - إدخال البيانات الأساسية: الاسم، الوصف (باستخدام محرر Rich Text)، السعر، SKU.
    - تعيين المنتج إلى فئة.
    - رفع صور متعددة للمنتج.
    - تحديد بيانات SEO (عنوان ووصف الصفحة).
- **إدارة الخيارات والمتغيرات:**
    - إنشاء خيارات للمنتج (مثل: المقاس).
    - إضافة قيم للخيارات (مثل: S, M, L).
    - إنشاء متغيرات (Variants) من تركيبة الخيارات (مثل: T-Shirt, Red, M).
    - تحديد سعر ومخزون و SKU خاص لكل متغير.
- **إدارة المخزون:**
    - عرض كمية المخزون لكل متغير.
    - إمكانية تعديل المخزون يدوياً.
- **إدارة الفئات:**
    - إنشاء وتعديل وحذف الفئات.
    - دعم الفئات الفرعية (Nested Categories).
- **قائمة المنتجات:**
    - عرض جميع المنتجات في جدول.
    - البحث عن منتج بالاسم أو SKU.
    - تصفية المنتجات حسب الفئة أو الحالة (نشط، غير متوفر).

### الواجهة الأمامية (Storefront)
- **صفحة الفئة:**
    - عرض المنتجات التي تنتمي إلى فئة معينة.
    - عرض عدد المنتجات في كل فئة.
- **صفحة المنتج:**
    - عرض جميع تفاصيل المنتج والصور.
    - واجهة لاختيار متغيرات المنتج (مقاس، لون).
    - تحديث السعر والصورة وحالة التوفر ديناميكياً عند تغيير الخيار.
    - عرض رسالة "غير متوفر" إذا كان المخزون صفراً.

## 3. منطق العمل (Business Logic)
- يجب أن يتم إنشاء `slug` فريد تلقائياً من اسم المنتج/الفئة.
- يتم خصم المخزون تلقائياً عند تقديم طلب مؤكد.
- يتم استرجاع المخزون عند إلغاء الطلب.
- لا يمكن للعميل إضافة منتج غير متوفر إلى السلة.
- إذا كان للمنتج متغيرات، يجب على العميل اختيار متغير قبل الإضافة إلى السلة.
