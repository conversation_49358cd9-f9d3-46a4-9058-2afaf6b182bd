# تفصيل الميزات: التقييمات والمراجعات

## 1. نظرة عامة
نظام يسمح للعملاء بتقديم تقييمات ومراجعات للمنتجات التي اشتروها، مما يزيد من ثقة العملاء الجدد ويقدم ملاحظات قيمة لمدير المتجر.

## 2. المتطلبات الوظيفية

### الواجهة الأمامية (العميل)
- **إضافة تقييم:**
    - يمكن للعميل إضافة تقييم للمنتج فقط بعد شرائه وتوصيله.
    - نموذج التقييم يحتوي على:
        - تقييم بالنجوم (من 1 إلى 5).
        - حقل لكتابة تعليق.
        - (اختياري) إمكانية رفع صورة للمنتج.
- **عرض التقييمات:**
    - في صفحة تفاصيل المنتج، يتم عرض متوسط التقييم الإجمالي.
    - عرض قائمة بجميع التقييمات المنشورة، مع اسم المقيّم (الاسم الأول فقط للخصوصية)، التقييم، والتعليق.

### لوحة التحكم (المسؤول)
- **إدارة التقييمات:**
    - قائمة بجميع التقييمات المستلمة مع حالتها (معلق، منشور، مرفوض).
    - إمكانية قراءة كل تقييم والموافقة على نشره أو رفضه.
    - إمكانية الرد على تقييم العميل (سيظهر الرد تحت التقييم الأصلي).
    - إمكانية حذف التقييمات.
- **إعدادات التقييم:**
    - خيار للنشر التلقائي للتقييمات (بدون مراجعة).
    - خيار للسماح للعملاء بإرفاق صور مع تقييماتهم.

## 3. منطق العمل (Business Logic)
- يتم ربط كل تقييم بالعميل والمنتج والطلب الأصلي.
- يتم حساب متوسط التقييم للمنتج تلقائياً عند إضافة أو تحديث تقييم منشور.
- بشكل افتراضي، لا يتم نشر التقييمات إلا بعد موافقة المسؤول (`is_published = False`).
- يتم إرسال إشعار للمسؤول عند استلام تقييم جديد.
