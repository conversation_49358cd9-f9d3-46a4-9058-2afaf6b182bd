# تفصيل الميزات: إدارة الطلبات

## 1. نظرة عامة
تغطي هذه الميزة دورة حياة الطلب بالكامل، من لحظة إنشائه بواسطة العميل حتى اكتمال توصيله، بالإضافة إلى عمليات الإرجاع والإلغاء.

## 2. التصميم الفني المرتبط
-   **مخطط قاعدة البيانات:** [[../01 - System Design/Database Schema/03 - Order Management]]
-   **نقاط نهاية الـ API:** [[../01 - System Design/API Endpoints (DRF)/03 - Cart & Checkout API]], [[../01 - System Design/API Endpoints (DRF)/05 - Admin API]]
-   **سير العمل:** [[../01 - System Design/Workflows/01 - Order Lifecycle]]

## 3. المتطلبات الوظيفية
...

### الواجهة الأمامية (العميل)
- **إنشاء طلب:**
    - يتم إنشاء الطلب بعد إتمام عملية الدفع بنجاح.
    - يتم إنشاء رقم طلب فريد.
- **سجل الطلبات:**
    - يمكن للعميل رؤية قائمة بجميع طلباته وحالة كل طلب.
    - يمكن للعميل عرض التفاصيل الكاملة لأي طلب.
- **إشعارات الطلب:**
    - يستلم العميل إشعاراً عبر البريد الإلكتروني عند تأكيد الطلب وعند كل تغيير مهم في حالته (تم الشحن، تم التوصيل).

### لوحة التحكم (المسؤول)
- **قائمة الطلبات:**
    - عرض جميع الطلبات في جدول مع معلومات أساسية (رقم الطلب، العميل، المبلغ، الحالة، التاريخ).
    - فلاتر متقدمة للبحث عن الطلبات (حسب الحالة، نطاق التاريخ، العميل).
- **تفاصيل الطلب:**
    - عرض جميع تفاصيل الطلب: المنتجات المطلوبة، معلومات العميل، عنوان الشحن، طريقة الدفع.
    - إمكانية تغيير حالة الطلب يدوياً (مثل: من "قيد المراجعة" إلى "قيد التجهيز").
    - إمكانية إضافة رقم تتبع الشحنة.
    - سجل داخلي (Log) يوضح تاريخ تغييرات حالة الطلب ومن قام بها.
- **إدارة الفواتير:**
    - إمكانية طباعة فاتورة الطلب.
- **إدارة الإرجاع:**
    - إمكانية بدء عملية إرجاع لطلب معين.
    - تتبع حالات الإرجاع (طلب إرجاع، تم استلام المرتجع، تم رد المبلغ).

## 3. منطق العمل (Business Logic)
- **حالات الطلب (Order Statuses):** يجب تحديد مسار واضح لحالات الطلب، مثل:
    1.  `Pending Payment`: بانتظار الدفع.
    2.  `Awaiting Review`: تم الدفع، بانتظار مراجعة المسؤول.
    3.  `In Preparation`: قيد التجهيز في المستودع.
    4.  `Shipped`: تم الشحن.
    5.  `Delivered`: تم التوصيل.
    6.  `Cancelled`: تم الإلغاء.
    7.  `Returned`: تم الإرجاع.
- يتم إرسال إشعارات تلقائية للعميل عند الانتقال بين الحالات الرئيسية.
- عند إلغاء الطلب أو إرجاعه، يجب استعادة كمية المخزون للمنتجات.
- لا يمكن تعديل الطلب بعد أن يدخل في مرحلة متقدمة (مثل "تم الشحن").
