# تفصيل الميزات: التسويق والعروض الترويجية

## 1. نظرة عامة
توفير أدوات تسويقية مرنة لمدير المتجر لزيادة المبيعات وجذب العملاء من خلال الخصومات، الكوبونات، والعروض الخاصة.

## 2. التصميم الفني المرتبط
-   **مخطط قاعدة البيانات:** [[../01 - System Design/Database Schema/04 - Customer & Marketing]]

## 3. المتطلبات الوظيفية
...

### نظام الكوبونات (Coupons)
- **لوحة التحكم (المسؤول):**
    - واجهة لإنشاء وتعديل الكوبونات.
    - **خيارات الكوبون:**
        - **رمز الكوبون:** رمز فريد (e.g., `RAMADAN20`).
        - **نوع الخصم:** نسبة مئوية (%) أو مبلغ ثابت.
        - **قيمة الخصم:** قيمة النسبة أو المبلغ.
        - **شروط التطبيق:**
            - الحد الأدنى لقيمة السلة.
            - تاريخ بدء وانتهاء الصلاحية.
            - حد الاستخدام (إجمالي ولكل عميل).
            - خيار الشحن المجاني.
        - **قيود التطبيق:**
            - استثناء أو قصر الاستخدام على منتجات أو فئات معينة.
            - قصر الاستخدام على مجموعات عملاء معينة.
- **الواجهة الأمامية (العميل):**
    - حقل في صفحة السلة أو الدفع لإدخال رمز الكوبون.
    - تطبيق الخصم على الإجمالي عند إدخال رمز صالح.

### العروض الخاصة (Special Offers)
- **لوحة التحكم (المسؤول):**
    - إمكانية تحديد سعر مخفض (`sale_price`) للمنتجات.
    - تحديد فترة زمنية للعرض (تاريخ بدء وانتهاء).
- **الواجهة الأمامية (العميل):**
    - عرض السعر الأصلي مشطوباً بجانب السعر المخفض على بطاقة المنتج وصفحة المنتج.
    - عرض لافتة أو شارة "عرض خاص" على المنتج.

### نقاط الولاء (Loyalty Points)
- **لوحة التحكم (المسؤول):**
    - تحديد عدد النقاط المكتسبة لكل مبلغ يتم إنفاقه.
    - إمكانية تعديل رصيد نقاط العميل يدوياً.
- **الواجهة الأمامية (العميل):**
    - عرض رصيد النقاط في صفحة حساب العميل.
    - إمكانية استبدال النقاط مقابل خصم عند الدفع.

## 3. منطق العمل (Business Logic)
- يتم التحقق من جميع شروط الكوبون قبل تطبيقه.
- لا يمكن الجمع بين أكثر من كوبون في نفس الطلب (عادةً).
- يتم منح نقاط الولاء فقط بعد اكتمال توصيل الطلب (`Delivered`).
