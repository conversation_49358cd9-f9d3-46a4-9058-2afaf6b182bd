# تفصيل الميزات: إعدادات المتجر

## 1. نظرة عامة
توفير واجهة مركزية في لوحة التحكم للمسؤول لتكوين الإعدادات العامة والتشغيلية للمتجر.

## 2. المتطلبات الوظيفية

### لوحة التحكم (المسؤول)
- **الإعدادات العامة:**
    - اسم المتجر وشعاره.
    - معلومات الاتصال (رقم الهاتف، البريد الإلكتروني، العنوان).
    - روابط وسائل التواصل الاجتماعي.
    - العملة الرئيسية للمتجر (e.g., SAR).
- **إعدادات الشحن:**
    - إدارة طرق الشحن المتاحة (Aramex, DHL, etc.).
    - إعد<PERSON> أسعار الشحن (سعر ثابت، أو متغير حسب الوزن/المنطقة).
    - ربط حسابات شركات الشحن عبر مفاتيح API.
- **إعدادات الدفع:**
    - تفعيل أو تعطيل بوابات الدفع المتاحة (Stripe, PayPal, Cash on Delivery).
    - إدخال مفاتيح API الخاصة بكل بوابة دفع.
- **إعدادات الضرائب:**
    - تحديد نسبة الضريبة المضافة (VAT).
    - خيار لتحديد ما إذا كانت الأسعار المعروضة تشمل الضريبة أم لا.
- **إعدادات الإشعارات:**
    - تخصيص قوالب رسائل البريد الإلكتروني التي يتم إرسالها للعملاء (تأكيد الطلب، تحديث الحالة، إلخ).
- **إعدادات متقدمة:**
    - زر لمسح ذاكرة التخزين المؤقت (Clear Cache) للتطبيق.

## 3. منطق العمل (Business Logic)
- يتم تخزين هذه الإعدادات في قاعدة البيانات (e.g., في جدول `StoreSetting` كأزواج مفتاح-قيمة).
- يتم تحميل هذه الإعدادات عند بدء تشغيل التطبيق وتكون متاحة للاستخدام في جميع أنحاء النظام.
- أي تغيير في هذه الإعدادات ينعكس فوراً على سلوك المتجر (مثل تغيير سعر الشحن أو تفعيل بوابة دفع جديدة).
