# تفصيل الميزات: إدارة العملاء

## 1. نظرة عامة
تتيح هذه الميزة إدارة بيانات العملاء، تصنيفهم، وفهم سلوكهم الشرائي لتقديم خدمة أفضل وحملات تسويقية موجهة.

## 2. التصميم الفني المرتبط
-   **مخطط قاعدة البيانات:** [[../01 - System Design/Database Schema/01 - User Management]]
-   **نقاط نهاية الـ API:** [[../01 - System Design/API Endpoints (DRF)/01 - Authentication API]], [[../01 - System Design/API Endpoints (DRF)/04 - User Profile API]]
-   **سير العمل:** [[../01 - System Design/Workflows/03 - User Registration & Verification]]

## 3. المتطلبات الوظيفية
...

### الواجهة الأمامية (العميل)
- **التسجيل وإنشاء حساب:**
    - نموذج تسجيل لإنشاء حساب جديد باستخدام البريد الإلكتروني وكلمة المرور.
- **تسجيل الدخول:**
    - مصادقة العميل للوصول إلى حسابه.
- **إدارة الحساب:**
    - صفحة للملف الشخصي تتيح للعميل تعديل معلوماته (الاسم، رقم الهاتف).
    - إمكانية تغيير كلمة المرور.
    - دفتر عناوين لإدارة عناوين الشحن المحفوظة.

### لوحة التحكم (المسؤول)
- **قائمة العملاء:**
    - عرض جميع العملاء المسجلين في جدول.
    - البحث عن عميل بالاسم، البريد الإلكتروني، أو رقم الهاتف.
- **ملف العميل:**
    - عرض صفحة تفصيلية للعميل تحتوي على:
        - معلومات الاتصال.
        - تاريخ التسجيل.
        - سجل كامل بجميع طلباته السابقة.
        - رصيد نقاط الولاء.
- **مجموعات العملاء:**
    - إمكانية إنشاء مجموعات لتصنيف العملاء (مثل: "عملاء VIP"، "عملاء جدد").
    - إمكانية تعيين عميل لمجموعة أو أكثر يدوياً.
    - (متقدم) تعيين العملاء تلقائياً بناءً على شروط (مثل: عدد الطلبات > 5).

## 3. منطق العمل (Business Logic)
- يجب أن يكون البريد الإلكتروني فريداً لكل حساب.
- يتم ربط كل طلب يتم إنشاؤه بحساب العميل الخاص به.
- يمكن استخدام مجموعات العملاء لتقديم عروض أو كوبونات خاصة بهم.
- يتم تسجيل تاريخ آخر تسجيل دخول للعميل.
