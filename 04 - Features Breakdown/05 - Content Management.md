# تفصيل الميزات: إدارة المحتوى (عبر Headless CMS)

## 1. نظرة عامة
تمكين مدير المتجر وفريق التسويق من إنشاء وإدارة المحتوى (الصفحات، البنرات، المقالات) بشكل مستقل عن فريق التطوير، وذلك باستخدام نظام إدارة محتوى منفصل (Headless CMS) مثل Strapi أو Sanity.

## 2. التصميم الفني المرتبط
-   **القرار المعماري:** [[../00 - Project Plan/05 - Architectural Decisions#ADR-003: فصل إدارة المحتوى باستخدام Headless CMS|ADR-003]]
-   **الجهة المسؤولة:** Headless CMS (لا توجد نماذج في قاعدة بيانات Django).

## 3. المتطلبات الوظيفية

### واجهة Headless CMS (للمسؤول)
-   واجهة مرئية لإنشاء وتعديل "أنواع المحتوى" (Content Types) مثل `Page`, `Banner`.
-   محرر نصوص متقدم (WYSIWYG) يسمح بإضافة نصوص منسقة، صور، وجداول.
-   إمكانية نشر أو إخفاء المحتوى.
-   دعم تعدد اللغات (i18n) للمحتوى.

### الواجهة الأمامية (العميل)
-   تقوم الواجهة الأمامية (Next.js) بجلب هذا المحتوى مباشرة من الـ API الخاص بالـ Headless CMS.
-   يتم عرض المحتوى في الصفحات المخصصة له (e.g., `/pages/[slug]`).

## 4. منطق العمل (Business Logic)
-   يتم استخدام `getStaticProps` في Next.js لجلب المحتوى عند بناء الموقع (Build time) وتحديثه بشكل دوري (ISR)، مما يضمن أداءً فائق السرعة.
-   يتم تأمين الـ API الخاص بالـ Headless CMS باستخدام مفاتيح API.
