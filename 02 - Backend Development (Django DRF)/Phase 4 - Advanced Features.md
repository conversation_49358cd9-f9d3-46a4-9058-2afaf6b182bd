# الواجهة الخلفية - المرحلة الرابعة: الميزات المتقدمة والتحليلات (أسابيع 13-16)

**التركيز على:** بناء ميزات تزيد من تفاعل العملاء، وتأسيس بنية تحتية لجمع البيانات.

## المهام

### الأسبوع 13: نظام الكوبونات والخصومات
- [ ] **(Backend)** كتابة اختبارات لمنطق الكوبونات.
- [ ] **(Backend)** إنشاء نموذج `Coupon` وتطوير نقاط نهاية API لإدارته (CRUD).
- [ ] **(Backend)** تطوير نقطة نهاية `POST /api/cart/apply-coupon/` للتحقق من صلاحية الكوبون وتطبيقه.

### الأسبوع 14: نظام نقاط الولاء
- [ ] **(Backend)** كتابة اختبارات لمنطق نقاط الولاء.
- [ ] **(Backend)** تطوير منطق منح واستبدال النقاط.
- [ ] **(Backend)** تطوير نقاط نهاية API لعرض سجل نقاط العميل.

### الأسبوع 15: البحث المتقدم والفلاتر
- [ ] **(Search)** تحسين نقطة نهاية البحث `GET /api/search/` لدعم:
    -   الفلترة المتقدمة (Faceted search) حسب الفئة والسعر والمتغيرات.
    -   الترتيب حسب الصلة، السعر، الأحدث.
    -   الإكمال التلقائي (Autocomplete).
- [ ] **(Performance)** تحسين أداء استعلامات Elasticsearch.

### الأسبوع 16: بنية التحليلات (Analytics Infrastructure)
- [ ] **(Analytics)** تصميم مخطط الأحداث (Event Schema) الرئيسية (e.g., `product_viewed`, `order_placed`).
- [ ] **(Analytics)** تطوير خدمة (Service) في Django لإرسال الأحداث إلى ناقل الرسائل (e.g., AWS Kinesis).
- [ ] **(Backend)** دمج إرسال الأحداث في النقاط الحيوية في الكود (مثل عند إنشاء طلب جديد، عرض منتج، تسجيل مستخدم).
- [ ] **(DevOps)** إعداد ناقل الرسائل وتكوين مسار لتوجيه الأحداث إلى مستودع البيانات (e.g., BigQuery).
