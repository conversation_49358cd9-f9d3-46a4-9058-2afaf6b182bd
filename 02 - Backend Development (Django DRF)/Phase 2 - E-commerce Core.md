# الواجهة الخلفية - المرحلة الثانية: وظائف التجارة الإلكترونية الأساسية (أسابيع 5-8)

## الأهداف
- تطوير نظام متغيرات المنتج والمخزون.
- بناء منطق سلة التسوق.
- إنشاء نظام الطلبات الأساسي.

## المهام

### الأسبوع 5: متغيرات المنتج (Product Variants)
- [ ] إنشاء نماذج قاعدة البيانات (Models) لـ:
    - [ ] `ProductOption` (مثل: اللون، المقاس)
    - [ ] `ProductOptionValue` (مثل: أحمر، XL)
    - [ ] `ProductVariant` (يجمع الخيارات مع سعر ومخزون خاص)
- [ ] تحديث `ProductSerializer` ونقاط نهاية API الخاصة بالمنتجات لتعكس المتغيرات والخيارات.
- [ ] تطوير منطق لتحديث سعر المنتج وصورته في الواجهة الأمامية بناءً على المتغير المختار.

### الأسبوع 6: إدارة المخزون (Inventory Management)
- [ ] إضافة حقل `stock` إلى نموذج `ProductVariant`.
- [ ] تطوير منطق لتقليل المخزون عند إنشاء طلب.
- [ ] تطوير منطق لإعادة المخزون عند إلغاء أو إرجاع طلب.
- [ ] إضافة فلاتر لنقاط نهاية المنتجات لإظهار المنتجات المتاحة فقط (`in_stock`).

### الأسبوع 7: سلة التسوق (Shopping Cart)
- [ ] تصميم منطق سلة التسوق (ستكون معتمدة على الجلسة للزوار، ومرتبطة بحساب المستخدم عند تسجيل الدخول).
- [ ] تطوير نقاط نهاية API لإدارة السلة:
    - [ ] `GET /api/cart/` (لعرض محتويات السلة)
    - [ ] `POST /api/cart/add/` (لإضافة منتج/متغير)
    - [ ] `PUT /api/cart/update/<item_id>/` (لتحديث الكمية)
    - [ ] `DELETE /api/cart/remove/<item_id>/` (لحذف عنصر)
- [ ] كتابة اختبارات الوحدات لمنطق سلة التسوق.

### الأسبوع 8: إنشاء الطلبات (Order Creation)
- [ ] إنشاء نماذج قاعدة البيانات (Models) لـ:
    - [ ] `Order`
    - [ ] `OrderItem`
- [ ] تطوير نقطة نهاية `POST /api/orders/` لإنشاء طلب جديد من السلة الحالية.
    - [ ] يجب أن تقوم هذه العملية بتفريغ السلة، تقليل المخزون، وحساب المبلغ الإجمالي.
- [ ] إعداد مهمة Celery لإرسال بريد إلكتروني لتأكيد الطلب للعميل.
- [ ] تطوير نقاط نهاية API أساسية للمسؤول لعرض الطلبات:
    - [ ] `GET /api/admin/orders/`
    - [ ] `GET /api/admin/orders/<id>/`
