# الواجهة الخلفية - المرحلة الثالثة: الدفع والتكاملات (أسابيع 9-12)

## الأهداف
- بناء منطق الدفع والشحن.
- التكامل مع بوابة دفع إلكتروني.
- تطوير نظام التقييمات والصفحات الثابتة.

## المهام

### الأسبوع 9: الشحن والدفع
- [ ] إنشاء نماذج قاعدة البيانات (Models) لـ:
    - [ ] `ShippingMethod`
    - [ ] `PaymentGateway`
- [ ] تطوير منطق لحساب تكلفة الشحن (في البداية ستكون ثابتة، ثم يمكن تطويرها لتعتمد على الوزن أو الموقع).
- [ ] تحديث نقطة نهاية إنشاء الطلب (`POST /api/orders/`) لتشمل اختيار طريقة الشحن والدفع.

### الأسبوع 10: التكامل مع بوابة الدفع
- [ ] اختيار بوابة دفع للتكامل (مثل Stripe).
- [ ] تطوير خدمة (Service Class) في Django للتعامل مع عمليات الدفع (إنشاء عملية دفع، التحقق من الحالة).
- [ ] إنشاء نقاط نهاية Webhook لاستقبال إشعارات من بوابة الدفع (مثل `payment_succeeded`, `payment_failed`).
- [ ] تحديث حالة الطلب (`payment_status` و `order_status`) بناءً على استجابة بوابة الدفع.
- [ ] تفعيل خيار "الدفع عند الاستلام" كطريقة دفع.

### الأسبوع 11: نظام التقييمات (Reviews)
- [ ] إنشاء نموذج قاعدة البيانات `Review`.
- [ ] تطوير نقاط نهاية API للتقييمات:
    - [ ] `POST /api/products/<product_id>/reviews/` (لإضافة تقييم، للمستخدمين الذين اشتروا المنتج فقط).
    - [ ] `GET /api/products/<product_id>/reviews/` (لعرض التقييمات المنشورة).
- [ ] تطوير نقاط نهاية API للمسؤول لمراجعة التقييمات:
    - [ ] `GET /api/admin/reviews/`
    - [ ] `PUT /api/admin/reviews/<id>/` (لتغيير حالة النشر).

### الأسبوع 12: الصفحات الثابتة وإدارة المحتوى
- [ ] إنشاء نموذج قاعدة البيانات `StaticPage`.
- [ ] تطوير نقاط نهاية API للمسؤول لإنشاء وتعديل الصفحات الثابتة (CRUD).
- [ ] تطوير نقطة نهاية عامة لعرض محتوى صفحة ثابتة بناءً على الـ `slug`:
    - [ ] `GET /api/pages/<slug>/`
