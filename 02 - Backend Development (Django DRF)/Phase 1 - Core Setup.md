# الواجهة الخلفية - المرحلة الأولى: البنية التحتية والبحث الأساسي (أسابيع 1-4)

**التركيز على:** تأسيس بنية تحتية قوية، تطبيق TDD، وإعداد خدمة بحث أساسية.

## المهام

### الأسبوع 1: إعداد المشروع والبنية التحتية
- [ ] **(DevOps)** تهيئة مشروع Django مع هيكلية قائمة على الميزات.
- [ ] **(DevOps)** إعداد Docker و `docker-compose.yml` لبيئة تطوير متكاملة (Django, PostgreSQL, Redis, **Elasticsearch**).
- [ ] **(Backend)** تكوين `django-environ` لإدارة متغيرات البيئة.
- [ ] **(CI/CD)** إعداد خط أنابيب CI/CD على GitHub Actions (Lint, Format, Test).

### الأسبوع 2: نظام المصادقة (TDD Approach)
- [ ] **(Backend)** كتابة اختبارات الوحدات أولاً لنقاط نهاية المصادقة.
- [ ] **(Backend)** إنشاء نماذج `CustomUser` و `CustomerProfile`.
- [ ] **(Backend)** تطوير نقاط نهاية API للمصادقة لتحقيق الاختبارات.
- [ ] **(Security)** تطبيق `Rate Limiting` على نقاط نهاية المصادقة.

### الأسبوع 3: إدارة المنتجات والفئات (مع دعم i18n)
- [ ] **(DB Design)** تصميم وتنفيذ نماذج `Category` (مع `django-mptt`), `Product`, `ProductVariant` لدعم تعدد اللغات باستخدام `JSONField`.
- [ ] **(Backend)** كتابة اختبارات للقراءة من نقاط نهاية المنتجات.
- [ ] **(Backend)** تطوير Serializers ونقاط نهاية API للقراءة فقط (Read-only) للمنتجات والفئات، مع دعم تحديد اللغة عبر `Accept-Language` header.
- [ ] **(Performance)** استخدام `select_related` و `prefetch_related` في `views` المنتجات.

### الأسبوع 4: إعداد البحث (Search Setup)
- [ ] **(Search)** إعداد وتكوين `django-elasticsearch-dsl` لربط نماذج `Product` مع Elasticsearch.
- [ ] **(Search)** إنشاء إشارات (Signals) لتحديث فهارس البحث تلقائياً عند إنشاء أو تحديث منتج.
- [ ] **(Backend)** تطوير نقطة نهاية API أساسية للبحث `GET /api/search/` التي تستعلم من Elasticsearch.
- [ ] **(DevOps)** إعداد خدمة Headless CMS (e.g., Strapi) وتشغيلها.
